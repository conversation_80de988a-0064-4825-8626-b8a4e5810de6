const net = require('net');
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const fs = require('fs');

// Express app setup
const app = express();
const defaultPort = 3000;

// Determine the correct path to the public folder
let publicPath;
if (process.env.NODE_ENV === 'development') {
  // Development mode - use the public folder from the project directory
  publicPath = path.join(__dirname, 'public');
} else if (process.resourcesPath) {
  // Running from packaged Electron app (production)
  publicPath = path.join(process.resourcesPath, 'app', 'public');
} else {
  // Fallback to the current directory (should work in most cases)
  publicPath = path.join(global.appRoot || __dirname, 'public');
}

// Check if the public directory exists at the determined path
if (fs.existsSync(publicPath)) {
  console.log(`Serving static files from: ${publicPath}`);
} else {
  console.error(`Public directory not found at path: ${publicPath}`);
  // Try alternative paths
  const alternatives = [
    path.join(__dirname, 'public'),
    path.join(__dirname, '..', 'public'),
    path.join(process.cwd(), 'public'),
    path.join(process.cwd(), 'resources', 'app', 'public')
  ];
  
  for (const alt of alternatives) {
    if (fs.existsSync(alt)) {
      console.log(`Using alternative public path: ${alt}`);
      publicPath = alt;
      break;
    }
  }
}

// Serve static files from the public directory
app.use(express.static(publicPath));

// Ensure index.html is served for all routes
app.get('*', (req, res) => {
  res.sendFile(path.join(publicPath, 'index.html'));
});

// Create HTTP server
const server = http.createServer(app);

// WebSocket server
const wss = new WebSocket.Server({ server });

// Router configuration
const routerConfig = {
  host: '*************',
  port: 4000
};

// Store names from router
const routerNames = {
  sources: {},
  destinations: {}
};

let routerClient = null;
let routerConnected = false;
let routerData = '';
let activeClients = new Set();
let manualDisconnect = false; // Track if disconnection was manual

// Connect to router
function connectToRouter() {
  if (routerClient) {
    routerClient.destroy();
  }
  
  console.log(`Connecting to router at ${routerConfig.host}:${routerConfig.port}`);
  
  routerClient = new net.Socket();
  
  routerClient.connect(routerConfig.port, routerConfig.host, () => {
    console.log('Connected to router');
    routerConnected = true;
    manualDisconnect = false; // Reset manual disconnect flag on successful connection
    broadcastToClients({
      type: 'connection',
      status: 'connected'
    });
    
    // Once connected, fetch the current crosspoint and lock status
    queryCurrentState();
  });
  
  routerClient.on('data', (data) => {
    const dataStr = data.toString();
    routerData += dataStr;
    
    // Process complete messages (they end with carriage return)
    while (routerData.includes('\r')) {
      const endIndex = routerData.indexOf('\r');
      const message = routerData.substring(0, endIndex);
      routerData = routerData.substring(endIndex + 1);
      
      console.log('Received from router:', message);
      
      // Parse router responses for names
      processRouterResponse(message);
      
      // Broadcast the response to all clients
      broadcastToClients({
        type: 'routerResponse',
        message: message
      });
    }
  });
  
  routerClient.on('close', () => {
    console.log('Connection to router closed');
    routerConnected = false;
    broadcastToClients({
      type: 'connection',
      status: 'disconnected'
    });
    
    // Try to reconnect after 5 seconds, unless it was a manual disconnect
    if (!manualDisconnect) {
      console.log('Attempting automatic reconnection in 5 seconds...');
      setTimeout(connectToRouter, 5000);
    }
  });
  
  routerClient.on('error', (err) => {
    console.error('Router connection error:', err.message);
    broadcastToClients({
      type: 'connection',
      status: 'error',
      message: err.message
    });
  });
}

// Manually disconnect from router
function disconnectFromRouter() {
  if (routerClient) {
    console.log('Manually disconnecting from router');
    manualDisconnect = true; // Set flag to prevent auto-reconnect
    routerClient.destroy();
    routerClient = null;
    routerConnected = false;
    broadcastToClients({
      type: 'connection',
      status: 'disconnected'
    });
  }
}

// Process router responses
function processRouterResponse(message) {
  // Source name response format: .RAS{source},{name} or .RAS{name}
  if (message.startsWith('.RAS')) {
    const matches = message.match(/\.RAS(\d+)(?:,\s*(.+))?/) || message.match(/\.RAS(.+)/);
    if (matches) {
      // If format is .RAS{source},{name}
      if (matches.length === 3 && matches[2]) {
        const sourceNum = parseInt(matches[1]);
        const sourceName = matches[2].trim();
        routerNames.sources[sourceNum] = sourceName;
      }
      // If format is .RAS{name}
      else if (matches.length === 2) {
        const sourceName = matches[1].trim();
        // Since we can't determine the source number, store with last requested source
        if (lastRequestedSource) {
          routerNames.sources[lastRequestedSource] = sourceName;
          lastRequestedSource = null;
        }
      }
      
      // Broadcast the updated names
      broadcastToClients({
        type: 'sourceNames',
        names: routerNames.sources
      });
    }
  }
  
  // Destination name response format: .RAD{dest},{name} or .RAD{name}
  if (message.startsWith('.RAD')) {
    const matches = message.match(/\.RAD(\d+)(?:,\s*(.+))?/) || message.match(/\.RAD(.+)/);
    if (matches) {
      // If format is .RAD{dest},{name}
      if (matches.length === 3 && matches[2]) {
        const destNum = parseInt(matches[1]);
        const destName = matches[2].trim();
        routerNames.destinations[destNum] = destName;
      }
      // If format is .RAD{name}
      else if (matches.length === 2) {
        const destName = matches[1].trim();
        // Since we can't determine the destination number, store with last requested destination
        if (lastRequestedDest) {
          routerNames.destinations[lastRequestedDest] = destName;
          lastRequestedDest = null;
        }
      }
      
      // Broadcast the updated names
      broadcastToClients({
        type: 'destinationNames',
        names: routerNames.destinations
      });
    }
  }
}

// Track last requested source/destination for name responses
let lastRequestedSource = null;
let lastRequestedDest = null;

// WebSocket connection handler
wss.on('connection', (ws) => {
  console.log('Client connected');
  activeClients.add(ws);
  
  // Send the current connection status to the new client
  ws.send(JSON.stringify({
    type: 'connection',
    status: routerConnected ? 'connected' : 'disconnected'
  }));
  
  // If we have any source/destination names, send them
  if (Object.keys(routerNames.sources).length > 0) {
    ws.send(JSON.stringify({
      type: 'sourceNames',
      names: routerNames.sources
    }));
  }
  
  if (Object.keys(routerNames.destinations).length > 0) {
    ws.send(JSON.stringify({
      type: 'destinationNames',
      names: routerNames.destinations
    }));
  }
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log('Received from client:', data);
      
      if (data.command === 'connectRouter') {
        console.log('Manual connection request received');
        // Only connect if not already connected
        if (!routerConnected) {
          connectToRouter();
        }
        return;
      } else if (data.command === 'disconnectRouter') {
        console.log('Manual disconnection request received');
        disconnectFromRouter();
        return;
      }
      
      if (data.command && routerConnected) {
        // Handle name query commands
        if (data.command.startsWith('.RS')) {
          // Track the requested source for response handling
          const sourceMatch = data.command.match(/\.RS(\d+)/);
          if (sourceMatch) {
            lastRequestedSource = parseInt(sourceMatch[1]);
          }
        } else if (data.command.startsWith('.RD')) {
          // Track the requested destination for response handling
          const destMatch = data.command.match(/\.RD(\d+)/);
          if (destMatch) {
            lastRequestedDest = parseInt(destMatch[1]);
          }
        }
        
        // Send command to router
        console.log('Sending to router:', data.command);
        routerClient.write(data.command + '\r');
        
        // Broadcast to all clients
        broadcastToClients({
          type: 'routerCommand',
          command: data.command
        });
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });
  
  ws.on('close', () => {
    console.log('Client disconnected');
    activeClients.delete(ws);
  });
});

// Broadcast message to all connected WebSocket clients
function broadcastToClients(data) {
  const message = JSON.stringify(data);
  activeClients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(message);
    }
  });
}

// Query current state from router
function queryCurrentState() {
  // Query which input is connected to output 1
  sendRouterCommand('.IV1');
  
  // Query lock status of output 1
  sendRouterCommand('.BI1');
  
  // Query names of inputs 1 and 2 (these are the ones we're using)
  sendRouterCommand('.RS1');
  sendRouterCommand('.RS2');
  
  // Query name of output 1
  sendRouterCommand('.RD1');
}

// Send command to router
function sendRouterCommand(command) {
  if (routerConnected) {
    console.log('Sending to router:', command);
    routerClient.write(command + '\r');
  } else {
    console.error('Cannot send command, not connected to router');
  }
}

// Try multiple ports if the default one is in use
function startServer(port) {
  server.listen(port)
    .on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.log(`Port ${port} is in use, trying port ${port + 1}`);
        startServer(port + 1);
      } else {
        console.error('Server error:', error);
      }
    })
    .on('listening', () => {
      const actualPort = server.address().port;
      console.log(`Server running on http://localhost:${actualPort}`);
      connectToRouter();
    });
}

// Start server on default port, with fallback to other ports if needed
startServer(defaultPort);