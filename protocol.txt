--- PAGE 1 ---

Quartz Routing Switcher
Remote

Control Protocol

QUICK REFERENCE GUIDE

Copyright 2006-2017

EVERTZ MICROSYSTEMS LTD.
5288 <PERSON> Drive

Burlington, Ontario

Canada L7L 5Z9


"Phone:
","************
"
"Sales:
","<EMAIL>
"
"Tech Support:
","<EMAIL>
"
"Web Page:
","http://www.evertz.com
"

evertz

Version 1.6, September 2017


"Fax:
","************
"
"Fax:
","************
"

The material contained in this manual consists of information that is the property of Evertz Microsystems and is intended solely for the use of
purchasers of the Router products.
Evertz Microsystems expressly prohibits the use of this manual for any purpose other than the operation of the
Router product.
Due to on going research and development, features and specifications in this manual are subject to change without notice.
All rights reserved. No part of this publication may be reproduced without the express written permission of Evertz Microsystems Ltd. Copies of
this manual can be ordered from your Evertz dealer or from Evertz Microsystems.

--- PAGE 2 ---

This page left intentionally blank

--- PAGE 3 ---

[Image 1]

4

IMPORTANT SAFETY INSTRUCTIONS

The lightning flash with arrowhead symbol within an equilateral triangle is
intended to alert the user to the presence of uninsulated "Dangerous voltage"
within the product's enclosure that may be of sufficient magnitude to constitute
a risk of electric shock to persons.
The exclamation point within an equilateral triangle is intended to alert the user
to the presence of important operating and maintenance (Servicing) instructions
in the literature accompanying the product.
Read these instructions

Keep these instructions.

Heed all warnings.

Follow all instructions.
Do not use this apparatus near water

Clean only with dry cloth.

Do not block any ventilation openings.
Install in accordance with the manufacturer's instructions.
Do not install near any heat sources such as radiators, heat registers, stoves, or other apparatus
(including amplifiers) that produce heat.
• Do not defeat the safety purpose of the polarized or grounding-type plug.
A polarized plug has two
blades with one wider than other.
A grounding-type plug has two blades and a third grounding prong.
The wide blade or the third prong is provided for your safety.
If the provided plug does not fit into your
outlet, consult an electrician for replacement of the obsolete outlet.
• Protect the power cord from being walked on or pinched particularly at plugs, convenience
receptacles and the point where they exit from the apparatus.
Only use attachments/accessories specified by the manufacturer

Unplug this apparatus during lightning storms or when unused for long periods of time.
Refer all servicing to qualified service personnel. Servicing is required when the apparatus has been
damaged in any way, such as power-supply cord or plug is damaged, liquid has been spilled or
objects have fallen into the apparatus, the apparatus has been exposed to rain or moisture, does not
operate normally, or has been dropped.
WARNING

TO REDUCE THE RISK OF FIRE OR ELECTRIC SHOCK, DO NOT EXPOSE THIS APPARATUS
TO RAIN OR MOISTURE

WARNING

DO NOT EXPOSE THIS EQUIPMENT TO DRIPPING OR SPLASHING AND ENSURE THAT NO
OBJECTS FILLED WITH LIQUIDS ARE PLACED ON THE EQUIPMENT

WARNING

TO COMPLETELY DISCONNECT THIS EQUIPMENT FROM THE AC MAINS, DISCONNECT THE
POWER SUPPLY CORD PLUG FROM THE AC RECEPTACLE

WARNING

THE MAINS PLUG OF THE POWER SUPPLY CORD SHALL REMAIN READILY OPERABLE

--- PAGE 4 ---

[Image 2]

INFORMATION TO USERS IN EUROPE

NOTE

CISPR 22 CLASS A DIGITAL DEVICE OR PERIPHERAL

This equipment has been tested and found to comply with the limits for a Class A digital device, pursuant
to the European Union EMC directive.
These limits are designed to provide reasonable protection
against harmful interference when the equipment is operated in a commercial environment.
This
equipment generates, uses, and can radiate radio frequency energy and, if not installed and used in
accordance with the instruction manual, may cause harmful interference to radio communications.
Operation of this equipment in a residential area is likely to cause harmful interference in which case the
user will be required to correct the interference at his own expense.
EN60065

CE

EN55103-1:
1996
EN55103-2:
1996

Safety
Emission

Immunity

EN504192 2005

Waste electrical products
should not be disposed of
with household waste.
Contact your Local Authority
for recycling advice

INFORMATION TO USERS IN THE U.S.A.

NOTE

FCC CLASS A DIGITAL DEVICE OR PERIPHERAL

This equipment has been tested and found to comply with the limits for a Class A digital device, pursuant
to Part 15 of the FCC Rules.
These limits are designed to provide reasonable protection against harmful
interference when the equipment is operated in a commercial environment.
This equipment generates,
uses, and can radiate radio frequency energy and, if not installed and used in accordance with the
instruction manual, may cause harmful interference to radio communications.
Operation of this
equipment in a residential area is likely to cause harmful interference in which case the user will be
required to correct the interference at his own expense.
WARNING

Changes or Modifications not expressly approved by Evertz Microsystems Ltd. could void the user's
authority to operate the equipment.
Use of unshielded plugs or cables may cause radiation interference.
Properly shielded interface cables
with the shield connected to the chassis ground of the device must be used.

--- PAGE 5 ---

evertz


"1.
","INTRODUCTION
","1
"
,"1.1. SERIAL (RS232/422) PHYSICAL INTERFACE.
1.1.1. Quartz D9 Serial Pin-Out.
1.1.2. PC-to-Quartz RS232 Interface Cable
1.1.3. Embedded Control System (FU-0003) Interface.
","1


1


2


2
"
"2.
","ETHERNET PHYSICAL INTERFACE
","5
"
"3


.
","COMMANDS
","7
"
"





























4.


","3.1.


SET XPT MESSAGE
3.1.1. Multiple Set Xpt Message


SYSTEM DESTINATION LOCK


3.2.




3.3. FIRE SYSTEM SALVO..


3.4.
INTERROGATE ROUTE


CONNECT ROUTES


3.5.


3.6. LIST ROUTES


3.7. READ NAME TABLE


WRITE NAME TABLE


3.8.


3.9. READ CONFIGURATION (EMBEDDED CONTROL SYSTEM ONLY)..


3.10. WRITE CONFIGURATION (EMBEDDED CONTROL SYSTEM ONLY)


3.11. QUEUE (SALVO) COMMAND...
3.11.1. Creating the Salvos
3.11.2. Emptying a Salvo
3.11.3. Loading a Salvo..
3.11.4. Fire a Salvo
3.11.5. Destroy a Salvo


3.12. LIST THE NUMBER OF ITEMS IN A SALVO.


3.13. GENERAL ENGINEERING COMMANDS


3.14. VIDEO STATUS DISPLAY


3.15. ENGINEERING COMMANDS..
","7


8


9


9


10


10


10


11


12


13


13


14


14


14


14


15


15


15


17


17
"












,"RESPONSES
","19
"
,"ACKNOWLEDGE


4.1.
","19
"
,"4.2. ERRORS
","19
"
,"POWER UP


4.3.
","19
"
,"UPDATE..


4.4.
","19
"
,"4.4.1. Tieline Update
","19
"

Application Note 65

Quartz Routing Switcher Remote Control Protocol

TABLE OF CONTENTS

Revision 1.6

--- PAGE 6 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol


"5.
","CHANGES BY PANELS
","23
"
"6.
","EXAMPLES..
",".25
"
"7.
","TIMING (EMBEDDED CONTROL SYSTEMS).
","27
"
"8.
","OLDER SYSTEMS
","29
"
"9.
","APPENDIX A
","31
"
"10.
","ADDENDUM: MAGNUM COMMAND EXTENTIONS..
","35
"
,"10.1. EXTENSION SUPPORT QUERY
","35
"
,"10.2. LOCKS AND PROTECTS
","35
"

evertz

Revision 1.6

--- PAGE 7 ---

evertz

Application Note 65

Quartz Routing Switcher Remote Control Protocol

REVISION HISTORY

REVISION

DESCRIPTION

DATE


"1.2.5
","Updates throughout
","March 2006
"
"1.2.6
","Updates throughout
","July 2006
"
"1.2.7


1.2.8
","Changes to the Queue command.
Change the QS command changed to.QF command.
Change the loading of salvo crosspoints from
the.SVxx,yy command $QSxx,y$ command.
This change is not backwards compatible.
Changes to Queue commands to remove reset
and destroy all if 0 specified.
Added fire at specified time command.
","October 2009


October 2009
"
"1.2.9
",".QC{n}(cr) command reply changed from $.QC\{n\}(cr)$ to $.A(cr)$
","January 2010
"
"1.3
","Added appendix A, supported commands
","June 2012
"
"1.3.1
","Added notes regarding obsolete commands, adjusted appendix
A regarding support of.L for certain routers
","June 2012
"
"1.4
","Added SC2000 information
","Nov 2012
"
"1.5
","Added notes for MAGNUM concerning number of
levels and responses
","Apr 2016
"
"1.6
","Added MAGNUM Command Extensions
","Sep 2017
"

Information contained in this manual is believed to be accurate and reliable.
However, Evertz assumes no responsibility for the use thereof nor
for the rights of third parties, which may be affected in any way by the use thereof.
Any representations in this document concerning
performance of Evertz products are for informational use only and are not warranties of future performance, either expressed or implied.
The
only warranty offered by Evertz in relation to this product is the Evertz standard limited warranty, stated in the sales contract or order
confirmation form.
Although every attempt has been made to accurately describe the features, installation and operation of this product in this manual, no
warranty is granted nor liability assumed in relation to any errors or omissions unless specifically undertaken in the Evertz sales contract or
order confirmation.
Information contained in this manual is periodically updated and changes will be incorporated into subsequent editions.
If
you encounter an error, please notify Evertz Customer Service department.
Evertz reserves the right, without notice or liability, to make
changes in equipment design or specifications.

Revision 1.6

--- PAGE 8 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

This page left intentionally blank

Revision 1.6

--- PAGE 9 ---

evertz

1.

INTRODUCTION

Application Note 65

Quartz Routing Switcher Remote Control Protocol

This document specifies a protocol suitable for simple control of a Quartz routing system by a computer
or third party system.
The protocol can use any of the physical interfaces available on the system,
either a RS232/422 interface or Ethernet.
As the protocol is ASCII text based, remote changes can be made using a terminal or a computer
running terminal emulation software.
For most applications only the .S command will be required to set
crosspoints, with the I or .L command to interrogate crosspoints.
The #01 command may be used at
system start up to check the RS232/422 link.

1.1.
SERIAL (RS232/422) PHYSICAL INTERFACE

Most Quartz products have a built in serial port that is link selectable between RS422 and RS232 mode.
For older routing products see the section at the end of this document.

1.1.1.
Quartz D9 Serial Pin-Out

The full router pin-out is shown below:


,"RS232
"
"Pin
","Signal
"
"1
","CHASSIS
"
"2
","RTS
"
"3
","RXD
"
"4
","N/C
"
"5
","N/C
"
"6
","GND
"
"7
","TXD
"
"8
","CTS
"
"9
","N/C
"


,"RS422
"
"Pin
","Signal
"
"1
","CHASSIS
"
"2
","TX-
"
"3
","RX+
"
"4
","RX GND
"
"5
","N/C
"
"6
","TX GND
"
"7
","TX+
"
"8
","RX-
"
"9
","N/C
"

Revision 1.6

Page 1

--- PAGE 10 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

everlz

For information, the following pin-out is normally used on personal computers, but check your own
computer's documentation.


,"D9 PLUG
"
"Pin
","Signal
"
"1
","DCD
"
"2
","RXD
"
"3
","TXD
"
"4
","DTR
"
"5
","GND
"
"6
","DSR
"
"7
","RTS
"
"8
","CTS
"
"9
","RI
"


,"D25 PLUG
"
"Pin
","Signal
"
"2
","TXD
"
"3
","RXD
"
"4
","RTS
"
"5
","CTS
"
"6
","DSR
"
"7
","GND
"
"8
","DCD
"
"20
","DTR
"
"22
","RI
"

1.1.2. PC-to-Quartz RS232 Interface Cable

The cable between a PC with a D9 connector and the router:

PC
D9 Plug


"D9 Skt
","Cable
","D9 Plug
"
"2 (RXD)
",,"7 (TXD)
"
"3 (TXD)
",,"3 (RXD)
"
"5 (0V)
",,"6 (0V)
"
"5 (GND)
",,

Router
D9 Skt

The cable between a PC with a D25 connector and the router:

PC

D25 Skt

Cable

D9 Plug

D25 Plug

Router
D9 Skt

3 (RXD)

7 (TXD)

2 (TXD)

3 (RXD)

7 (0V)

6 (0V)

5 (GND)

1.1.3.
Embedded Control System (FU-0003) Interface

*******.

Router DIP Switch Settings

DIP switch 2 (DIP-2) must be down to enable the computer port.
The port will then only respond to
messages of the correct format as detailed in this protocol document.

Page 2

Revision 1.6

--- PAGE 11 ---

everlz

Application Note 65

Quartz Routing Switcher Remote Control Protocol

DIP-1

Up Default baud rate (38400)

Down = SETUP defined baud rate

DIP-2

Up Normal position

Down = computer port enable

DIP-3

Up Slave router

Down = Master router

DIP-4

Up Normal position

Down

Re-boot with i/p X to all

o/p's

The normal setting for a master router is Up, Down, Down, Up and for a slave router Up, Down, Up, Up.
*******. Protocols

The embedded router/panel firmware EPROM has one remote control protocol built in to it.
The Quartz
standard ASCII protocol is indicated by a 1 at the end of the EPROM number i.e. PC215-1.
ΠΠΠΠΠ

PC215-1
V5.00

 

Or

ΠΠΠΠΠ

PC150-1
V5.00

 

In later systems the embedded router/panel firmware FLASH device may have one or more remote
control protocol built in to it.
These protocols can be selected via the WinSetup configuration editor.
The port operates at 38400, 8 data bits, no parity, and 1 stop bit.
Other baud rates and parity options can
be selected from WinSetup.

Revision 1.6

Page 3

--- PAGE 12 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

This page left intentionally blank

Page 4

Revision 1.6

--- PAGE 13 ---

evertz

Application Note 65

Quartz Routing Switcher Remote Control Protocol

2.

ETHERNET PHYSICAL INTERFACE

More recent Quartz products such as the EQX, SC-1000, Q256, Xenon and Topaz are equipped with
an Ethernet port.
These support 100base2 or 10base2 Ethernet using CAT5 cable. The network should
ideally be closed (or at least isolated with a suitable switch) for use with devices on the Quartz routing
system.
Both the physical interfaces (Serial and Ethernet) use the same protocol command and reply structure,
which is detailed below.
The following few paragraphs just describe the Ethernet configuration and
connection needed in order to communicate with these devices.
Each device on the network must be assigned a unique IP address.
Changing and interrogating the IP
addresses are usually sent via serial port commands as documented in 3.15.
If two controllers are
installed in the controller or routing frame two stream socket connections will be required to the main
and backup controllers, as either of the two might be in control of the routing system at any point in
time.
Normally Quartz controllers will allow a TCP/IP connection via the telnet port number (23).
On the SC-1000 additional TCP/IP ports can be added to the configuration file currently selected by the
SC-1000.
This is configured from WinSetup under the "System"->"SC-1000 Configuration"->"Options"
dialog. A TCP/IP port should be added using the Quartz protocol and at this point the user can specify
a port number for the conversation (please note this port number should be greater than 1024).
Please
make sure the server check box is also ticked.

Quartz controllers use standard TCP/IP stream sockets (sometimes referred to as Berkeley sockets) to
communicate with other network devices needing to control the routing system.
Stream sockets are
connection oriented, and so a connection must be opened and maintained for the duration of
communications.
Stream sockets are supported for many different host environments and operating
systems.
In order to start communication with a controller the computer or other host device must originate the
communications.
In other words the router controller will be the server and the computer will be the
client.
The client should:

1. Create a stream socket.

2. Connect the socket to the IP address of the desired main/reserve controller on the port specified
in the configuration.
(Note if there are redundant controllers, each controller will have its own
unique IP address and need a separate stream socket. However, only the active controller will
be able to accept a connection).
3. Once the connection has been successful, commands can then be sent to the routing controller
and the controller will reply according to the command sent as detailed in section 3.

4. Once the connection is finished with, it should be closed as normal.
Revision 1.6

Page 5

--- PAGE 14 ---

Application Note 65
Quartz Routing Switcher Remote Control Protocol

everlz

5. The routing controller will terminate all connections on a download of a new configuration so the
clients program should cope with losing and re-establishing this connection to the controller.
The SC-1000 controller allows the user to see a list of the currently active TCP/IP connections via both
the configuration port and the LCD window on the front of the unit.
Page 6

Revision 1.6

--- PAGE 15 ---

evertz

Application Note 65

Quartz Routing Switcher Remote Control Protocol

3.

COMMANDS

All characters used should be in upper case.
Values in { } brackets are variable fields. Values in []
brackets are optional variable fields.
Values in () brackets are non-printable characters i.e. carriage
return (cr). No space or tab characters are used.
All crosspoint numbering starts from one (1) and not
zero (0). The (cr) character is ASCII carriage return (code OD hex).
A reply is only generated after a
(cr).

Some examples are given of 'C' code suitable to output messages.
These are only intended to clarify the
command structure. They assume a separate 'C' routine RS232_printf which is similar to the standard
printf but with is output directed to the RS232 serial port of the host computer.
3.1. SET XPT MESSAGE

This allows a route to be made (crosspoint set) and uses the command format:

.S{level}{dest}, {srce}(cr)

The legal levels are V,A,B,C,D,E,F,G in 8 level systems and V,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O in 16 level
systems.
MAGNUM based control systems support up to 26 legal levels.

V,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,W,X,Y,Z


"TYPICAL MESSAGE Q1600 LEVEL


TYPICAL USE
"
".SV1,2(cr)


level 1
video
"
".SA2,4(cr)




audio 1
level 2
"
"audio 2
level 3


.SB4,1 (cr)
"
"level 4
control/time code


.SC31,12(cr)


"

The router will reply with a . U{level}{dest}, {srce) (cr) message.
The error message .E(cr) is generated if
the command is not recognised.


"SEND
","REPLY
",
".SV001,002(cr)
",".UV001,002(cr)
",
".SC9,3(cr)
",".UC009,003(cr)
",
".SZ1,1(cr)
",".E(cr)
","/* unknown level */
"
".MA1,1(cr)
",".E(cr)
","/* unknown command */
"

Multiple levels can be set by including more level identifiers in the message.
For MAGNUM based control
systems this could be up to 26 levels in a set, V,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,W,X,Y,Z


".SVA1,2(cr)
","level 1,2
"
".SAC1,2(cr)
","level 2,4
"
".SCV1,2(cr)
","level 4,1
"
".SVABC1,2(cr)
","level 1,2,3,4
"

The router will reply with an update message (see section 5.4)

.SBA1,2(cr)

UAB001,002(cr)

Revision 1.6

Page 7

--- PAGE 16 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

MAGNUM systems will reply with an update message for each level


".SBA1,2(cr)
",".UA001,002(cr)
"
,".UB001,002(cr)
"

everlz

In a Magnum System, when routing EMBEDDED AUDIO to an AVOP, a response regarding the audio
levels with Source 0 indicates that the AVOP is in "passthrough" mode (where the audio is from the
embedded audio of the source).
Example 'C' code to set a crosspoint on levels 1, 2, & 3.

$evel="VAB";$ 

$dest=5$ 

$srce=2$ 

/* levels to be controlled */

/* matrix output */

/* matrix input */

RS232_printf(".S%s%d,%d\r", level, dest, srce);
3.1.1. Multiple Set Xpt Message

This command allows a block of up to 16 routes to be set from one command providing that the
command does not exceed 256 bytes in length.
The command allows the level to be optionally specified
for any of the routes, if the level is not specified for the first destination then it assumes all levels for that
destination will be routed to the source given, if a level is not specified for a subsequent destination then it
assumes the last level defined for a destination.
The format of the command is given below :

.M{destination}, {source}, {destination}, {source .... (cr)
$where\{destination\rangle=[level,]\langle dest,\rangle[+\langle destination\rangle][-(dest_{3}\rangle[+\langle destination\rangle]]$ 
$lere\{source\}=\{srce_{1}\}[+\{source\}][-\{srce_{3}\}[+\{source\}]]$ 

The, character is used to delimit a destination or a source. The '+' and '-' character can be used to
specify a number of parameters of the same type (destinations/ sources). The '+' character indicates
that another parameter of the same type will be added. The character indicates that the next
parameter of the same type defines the end of a range.

The simplest form of the command is given below:

.M[level]{dest₁}, {srce₁}, [level2]{dest2},{srce2},.....[level]{dest}, {srcen}(cr)
The command:

.M[level]{dest₁}+[level2]{dest2}+[level3]{dest3}, {srce₁}(cr)

Would set all three destinations $dest_{1,2,3}$ to the specified source.

The command:

.M[level]{dest₁}-{dest3}, 
{srce₁}(cr)

Would set all logical destinations in the range dest, to dest to the specified source on level,.
Note no
optional level is permitted on the end of a destination range.

Page 8

Revision 1.6

--- PAGE 17 ---

evertz

Application Note 65

Quartz Routing Switcher Remote Control Protocol

The command:

MVA001-005,010-014(cr)

Would set destination 1 to source 10, destination 2 to source 11, destination 3 to source 12, destination
4 to source 13 and destination 5 to source 14 on control levels 1 and 2.

The router replies to this command with either an acknowledge or error message.
The acknowledge
command will indicate purely that the command has been received and is being processed.
The routing
system will then process the route takes within the command and issue update messages for each
valid route request.
Please note that this command was added on version V1.17 (15/11/01) of the RCP-
T01 protocol.

3.2.
SYSTEM DESTINATION LOCK

This allows the specified destination to be locked within the system;
it will inhibit the destination being
changed on any level from any standard control panel in the system without turning this lock off.
This
command combines the functionality of locking, unlocking and interrogating the system destination locks.

To lock a destination use the command


".BL{dest}(cr)
","Lock a destination
"

To unlock a destination use the command


".BU{dest}(cr)
","Unlock a destination
"

To interrogate the systems destination lock status use the command

.BI{dest (cr)

Interrogate destination lock status

The router replies to all of the above commands with

.BA{dest}, {lock status} (cr) Destinations lock status

Where the  lock status field is in ASCII and consists of up to three characters.
The values of which
should be interpreted as follows:


"""0""
","destination unlocked
"
"""1""... ""254""


""255""
","protected destination lock, locked by panel at Q-link address
'n'-1
unprotected destination lock
"

3.3. FIRE SYSTEM SALVO

This allows the specified system salvo to be 'fired' within the system.
This is the same as the QF
command.

To 'fire' a system salvo use the command

Revision 1.6

Page 9

--- PAGE 18 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

.F{salvo}(cr)

Fire a Salvo

everlz

Where the  salvo field is in ASCII and consists of up to three numeric characters, the value of which
should be between 1 and 32.

The router replies to the command with

.A (cr)

3.4.
INTERROGATE ROUTE

This allows a single destination to be interrogated to find the current source routed to it

.I{level){dest (cr)

The router replies with

.A{level}{dest}, {srce (cr)

Please note that destinations that have been routed via tielines modify the returned source value to
indicate the control level the source has been routed from.
If systems ignore the level information then
only the lower 12 bits of the source value should be used.
For more information please refer to section
3.1.

Example 'C' code to interrogate a crosspoint on levels 1, 2, & 3.

level $=^{\prime\prime}V^{\prime\prime}$ 
$dest=5$ 

/* levels to be controlled */

/* matrix output */

RS232_printf(". %s%d\r", level, dest);
3.5. CONNECT ROUTES

This command is reserved for later use but will have the following format.
This allows up to 4 crosspoints to be set in a single command line.
.C{level}{dest}, {srce} {level}{dest , {srce .. (cr)

The lev, dest, srce part of the message can be repeated up to 4 times with a space between each group.
3.6.

LIST ROUTES

This command allows a block of up to 8 routes to be interrogated. The command has 2 formats.
In
both cases the command specifies a level and start destination. The router replies with a list of up to 8
routes.
Less than 8 routes are returned if the maximum destination is exceeded, or the search
conditions are not met.
Format (1): List from the specified destination showing current sources.
.L{level}{dest ,-(cr)

Page 10

Revision 1.6

--- PAGE 19 ---

evertz

The router replies with

Application Note 65

Quartz Routing Switcher Remote Control Protocol

.A{level}{dest}, {srce}{level}{dest}, {srce ...  level}{dest}, {srce}(cr)

Example 'C' code to list routes (format 1).
$level="V"$ 

dest $=5$;

/* levels to be controlled */

/* matrix output */

RS232_printf(".L%s%d,-\r", level, dest);
Format (2): List from the specified destination only those destinations using the specified source.
.L{level}{dest}, {srce}(cr)

The router replies with

.A{level}{dest}, {srce}{level}{dest}, {srce ... {level}{dest}, {srce (cr)

Example 'C' code to list routes (format 2).
level $I=^{\prime\prime}V^{\prime\prime}$ 

$dest=5;$ 

srce $=2$ 

/* levels to be controlled */

/* matrix output */

/* matrix input */

RS232_printf(".L%s%d,%d\r", level, dest, srce);
3.7. READ NAME TABLE

This allows the specified destination/source or level mnemonic to be read back from the system.
.RD{dest}(cr)

Read a destination mnemonic

Where the (dest field is an ASCII representation of a number between 1 and the maximum destination.


".RS{source (cr)
","Read a source mnemonic
"

Where the  source) field is an ASCII representation of a number between 1 and the maximum source.
.RL{level}(cr)

Read a level mnemonic

Where the (level field is one of the following ASCII characters: (V,A,B,C,D,E,F,G).

The router replies to the above commands with one of the following responses dependant on the
firmware, a comma delimiter should be checked for to determine which response has been sent.

.RA[D/S/L]{mnemonic string}(cr)

.RA[D/S/L]{dest/source/level}, {mnemonic string (cr)

Where the {mnemonic string field is in ASCII and consists of eight characters. The string is
delimited by whitespace.

The following commands allow the specified 10 character destination/source or level mnemonic to be
read back from the system. These mnemonics appear on the LCD button range of panels and are
displayed in a two rows of five 
character orientation.

Revision 1.6

Page 11

--- PAGE 20 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

.RE{dest}(cr)

Read a destination mnemonic

everlz

where the (dest field is a ASCII representation of a number between 1 and the maximum destination.


".RT{source)(cr)
","Read a source mnemonic
"

where the (source) field is an ASCII representation of a number between 1 and the maximum source.


".RM{level}(cr)
","Read a level mnemonic
"

where the {level} field is one of the following ASCII characters: (V, A, B, C, D, E, F, G).
The router replies to the above commands with one of the following responses dependant on the
firmware, a comma delimiter should be checked for to determine which response has been sent.
.RA[E/T/M]{mnemonic string}(cr)

.RA[E/T/M]{dest/source/level}, {mnemonic string}(cr)

Where the  mnemonic string field is in ASCII and consists of ten characters.
The string is
delimited by whitespace.

3.8. WRITE NAME TABLE

The following commands allow the specified 8 character destination/source or level mnemonic to be
changed in the system.


".WD{dest}, {mnemonic string}(cr)
","Write a destination mnemonic
"
".WS source}, {mnemonic string}(cr)
","Write a source mnemonic
"
".WL level}, {mnemonic string}(cr)
","Write a level mnemonic
"

Where the  dest}/{source field is a ASCII representation of a number between 1 and the maximum
destination/source.
Where the  level} field is one of the following ASCII characters: (V,A,B,C,D,E,F,G).
Where the {mnemonic string field is in ASCII and consists of eight characters. The string is
delimited by whitespace.
The router replies to the above commands with an update to the router name in the form shown below
and then an acknowledge.
The router replies to the above commands with

.RA[D/S/L]{dest/source/level}, {mnemonic string}(cr)

followed by an acknowledge response.

Page 12

Revision 1.6

--- PAGE 21 ---

evertz

Application Note 65
Quartz Routing Switcher Remote Control Protocol
The following commands allow the specified 10 character destination/source or level mnemonic to be
changed in the system.
These mnemonics appear on the LCD button range of panels and are displayed
in a two rows of five character orientation.


".WE dest}, {mnemonic string (cr)
","Write a destination mnemonic
"
".WT{source}, {mnemonic string}(cr)
","Write a source mnemonic
"
".WM{level}, {mnemonic string}(cr)
","Write a level mnemonic
"

Where the  dest}/{source field is a ASCII representation of a number between 1 and the maximum
destination/source.
Where the  level field is one of the following ASCII characters: (V,A,B,C,D,E,F,G).
Where the {mnemonic string field is in ASCII and consists of ten characters. The string is
delimited by whitespace.
The router replies to the above commands with

.RA[E/T/M]{dest/source/level}, {mnemonic string}(cr)

followed by an acknowledge response.

3.9.
READ CONFIGURATION (EMBEDDED CONTROL SYSTEM ONLY)

This allows the routers internal configuration EPROM to be read back eight bytes at a time.
The legal
address range is 0 to 3FFFh.

.?{addr}(cr)

The router replies with

.A{addr}, {byte 1}, {byte 2 , ..., {byte 8}(cr)

3.10.
WRITE CONFIGURATION (EMBEDDED CONTROL SYSTEM ONLY)

This allows the routers internal configuration EPROM to be re-written.
For this option to work the router
must be fitted with EEPROM or NVRAM in place of the standard EPROM.
The legal address range is 0
to FFFFh in normal mode and 0 to 1EFFFh in extended addressing mode.
Between one and eight bytes
can be written on each command.
.!{addr}, {byte 1}, {byte 2}, ...,  byte 8}(cr)

The router replies with

.A{addr}, {byte 1}, {byte 2 , ..., {byte 8}(cr)

The addr parameter is 4 bytes long in normal mode and five bytes long in extended addressing mode.
Revision 1.6

Page 13

--- PAGE 22 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

3.11. QUEUE (SALVO) COMMAND

everlz

This command allows the user to dynamically create, destroy, modify and fire multiple salvos during
runtime.
These salvos are the same set as the system salvos created by WinSetup and fired using the
.F{salvo or .QF{salvo} command.
Winsetup salvos are created starting at Salvo 1 so it is good practice to
dynamically create salvos at higher salvo numbers e.g.
salvo 10, 11, 12 so as not to over write WinSetup
salvoes.
An empty salvo is first created using the QC command and then loaded with crosspoints using the QS
command.
Salvos are numbered from 1 to 32, with 0 having a special function that changes depending
on the command sent.
Each salvo can hold up to 2048 crosspoints. The number of crosspoints that can
be processed each TV frame depends on the hardware platform.
The video crosspoints are given priority
which means the serial and Ethernet ports may update at a slower rate depending on baud rate etc.

These commands are currently only supported on the SC-1000 (V2.36) and EQX (after Jan 2010).
The
SC-1000 prior to V2.36 used a slightly different command set which is detailed in V1.2.6 of this document.

3.11.1.
Creating the Salvos

.QC{n}(cr) - Changes current salvo to salvo n, creating the buffer it does not exist.
If n is zero, then the
currently selected salvo will be deselected.
If n is not specified, the router will return the currently
selected salvo

If the command succeeds, the router replies with:

.A(cr)

If the command fails, the router replies with $n=zero:$ 

.QC0(cr)

3.11.2.
Emptying a Salvo

.QR{n}(cr) - Resets the content of salvo n.

If the salvo selected exists the router will respond with:

.A(cr)

3.11.3.
Loading a Salvo

.QS{level}{dest}, {srce (cr) - Loads crosspoint data into the currently selected salvo, The format of the
S{level}{dest}, {srce (cr) part of the command is identical to that used for the standard Set Xpt message.
If the salvo set crosspoint succeeds the router will respond with:

.A(cr)

3.11.4. Fire a Salvo

Fire immediately:

Page 14

Revision 1.6

--- PAGE 23 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

.QF{n}(cr) - Fires salvo n.
If the salvo exists, the router will respond with:

.A(cr)

Or fire at specified time (assumes hardware support for time code reference):

.QF{n}T1:hh:mm:ss:ff(cr)
Fires salvo n at the specified time in hours, minutes, seconds, and
frames.
T1 specifies the system time to use where multiple times available (50Hz or 60Hz) are but
currently only T1 is supported.
Specifying a frame number that does not occur will mean the salvo
never fires e.g.
specifying ff $ff=28$ in a 25 frame system.

If the salvo exists, the router will respond with:

.A(cr)

3.11.5.
Destroy a Salvo

.QD{n}(cr) - Destroys salvo n.

If the salvo selected exists the router will respond with:

.A(cr)

3.12.
LIST THE NUMBER OF ITEMS IN A SALVO

.QL{n}(cr) - Lists the number of items in salvo n.
If n is not specified, the number of items in the current
salvo will be listed (if one is selected).
If the salvo selected exists, or a destroy all is sent, the router will respond with:

.QL{n}, {m}(cr) - Where n is the current salvo and m is the number of items in it.
This will be repeated
for each salvo that exists, if list all is selected.

Examples


"Action
","Command
","Response
"
"Create salvo 10, making it the currently active salvo
",".QC10(cr)
",".QC10(cr)
"
"Empty salvo 10 (make sure it is empty)
",".QR10(cr)
",".A(cr)
"
"Send crosspoint to salvo 10
",".QSV1,4(cr)
",".A(cr)
"
"Send crosspoint to salvo 10
",".QSV3,2(cr)
",".A(cr)
"
"Fire salvo 10
",".QF10(cr)
",".A(cr)
"
,,".UV1,4(cr)
"
,,".UV3,2(cr)
"
"Deselect salvo 10
",".QC10(cr)
",".QC10(cr)
"

3.13. GENERAL ENGINEERING COMMANDS

All the values in brackets  xx) are two digit hexadecimal numbers.

Revision 1.6

Page 15

--- PAGE 24 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

everlz


".#00(cr)
","Enquire the devices Q-Link Protocol version and address. The router replies with
$(.)(A)\{ver\},\{xx\}(cr).$ The xx parameter is the hexadecimal Q-link address of the
"
,"device and the ver parameter is the Q-link version number. 106 is equivalent to
V1.06.
"
".#01 (cr)
","Test if router is connected. The router replies with $(.)(A)(cr).$
"
".#02(cr)
","Put router in extended addressing mode. The router replies with $(.)(A)(cr).$
"
".#12, xx (cr)
","Reset router. This can be used after re-configuration. The xx parameter is
optional and causes all routes to be set to source xx.
"
".#23(cr)
","Halt vertical interval switching. This allows complex salvos to be set up to occur
in one vertical interval.
"
".#34(cr)
","Restart vertical interval switching. As above.
"
".#40,{ddd (cr)
","On-line update to source number ddd. Master replies with an Ack.
"
".#41, ddd}(cr)
","On-line update to destination number ddd. Master replies with an Ack.
"
".#42, ddd (cr)
","On-line update to source mnemonic number ddd. Master replies with an Ack.
"
".#43,{ddd (cr)
.#44,{xx}(cr)
","On-line update to destination mnemonic number ddd. Master replies with an Ack.


Force device xx offline. Master replies with an Ack.
"
"$.\#45,\{xx\}(cr)$
","Return Q-link status of device xx
.Axx,01 = on-line Axx,02 = off-line Axx,03 = this unit


.
"
"$.\#46,\{xx\}(cr)$
","Get general error number for address.
"
". $\#47,\{xx\},\{nn\}(cr)$
","Get specific error number occurrences for device.
"
"$.\#48,\{xx\}(cr)$
","Clears error count for device.
"
"$.\#49,\{xx\}(cr)$
","Get general report number for address (can then get details).
"
"$.\#50,\{xx\}(cr)$
","Get type and version numbers of Q-link device from master
uses stored
information retrieved from polling (or slave, but only own data).
"
".#51,{xx}(cr)
","Force master to enquire type and version numbers of Q-link device (uses
fetched data).
"
".#56(cr)
","Reserved (halt panel updates).
"
".#67(cr)


.#78(cr)
","Reserved (restart panel updates).
Reserved (enable extended protocol).
"
".#80,75(cr)
","Jump to boot loader.
"

Page 16

Revision 1.6

--- PAGE 25 ---

evertz

.#81(cr)

Application Note 65

Quartz Routing Switcher Remote Control Protocol

Boot loader command, DO NOT USE.

3.14.
VIDEO STATUS DISPLAY

These commands only work when the status display is operating in stand alone mode.
.VP-(cr)
$.VP+(cr)$ 
$.VP\{xx\}(cr)$ 

Display page decrement.

Display page increment.

Display page xx (01, 02, etc)

3.15.
ENGINEERING COMMANDS

All the values in brackets  xx) are two digit hexadecimal numbers.
.&LOCALTCPIP, *********** (cr)

.&REMOTETCPIP, *********** (cr)

.&TCPGATE, **********(cr)

.&TCPMASK, **********(cr)

.&LOCALTCPIP(cr)

Set TCP/IP address of the controller you are connected to.
Set TCP/IP address of the other controller in a dual
redundant configuration.

Sets TCP/IP address of the network gateway.
Sets TCP/IP address mask.

Sending any of the above four commands as shown
returns the address of the device in the same format
as the set commands.
Revision 1.6

Page 17

--- PAGE 26 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

This page left intentionally blank

Page 18

Revision 1.6

--- PAGE 27 ---

evertz

4.

RESPONSES

4.1.

Application Note 65

Quartz Routing Switcher Remote Control Protocol

ACKNOWLEDGE

Acknowledge is used as a no error response to some messages:

.A(cr)

ERRORS

4.2.
If an error is detected in any sequence prior to a (cr), the matrix replies with:

.E(cr)

4.3.
POWER UP

At power up or reset the matrix outputs a (cr).P(cr) to inform a remote computer that the matrix is now
on-line.
4.4.

UPDATE

If any routes are changed in the matrix, either by remote panels or the remote control protocol, the matrix
replies with an update message for each route changed.
For a normal take, where the destination and
sources are routed on the same control level updates are of the form:

.U{levels}{dest}, {srce (cr)

The response message always replies with levels in the following order: V,A,B,C,D,E,F,G...

4.4.1.
Tieline Update

This type of update was used exclusively on the SC1000 controller. The SC1000 is obsolete.
Since a tie-line take has a different level for the destination as for the source then the level information
is included as;
.U{dest level}{dest}, { {srce level ORed with {srce} }

In this circumstance there will be only one level defined for the source and destination.
The source value
will contain both the source number and the level from which it has been routed to.
When the source number is represented in hexadecimal, the lower 12 bits of the source value define the
source number (this gives the source number a range of 1-4095).
The next 5 bits define the level value
(between 1 and 31) of the tieline source.

Revision 1.6

Page 19

--- PAGE 28 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

For example, the take request:

.SVA1,2(cr)

Could result in the following response:

.UV001,8194(cr)

everlz

This response means that destination 1 on control level 1 ('V') is routed via a tieline to source 2 on control
level 2. As 8194 is 2002 in hexadecimal lower 12 bits is 002 or source 2, upper 5 bits is 2 or control level 2.

Below is a table that precise the responses from the commonly used routing commands:


"Command
","Command Data
","Normal Response/s
","Note
"
"Set Xpt
",".S{level}{dest}, {srce}
(cr)
","No explicit response
",".U response


conditional on
route being made
"
"Multiple Set Xpt
",".M{destination}, {sourc
e},
 destination}, {source)
","A
",".U response/s


conditional on
"
"Interrogate
Destination status
","(cr)


.I{level}{dest (cr)
",".A{level}{dest}, {srce)(cr)
","route being made


There will be no
response to an
interrogate
command if the
level or destination
is invalid on the
device being
interrogated. The
reason for this is
that many routers
may be connected
to one controlling
system. Some of
these routers may
respond and others
may not. If routers
responded with an
error message if
the destination /
level were invalid,
the controller could
receive error
messages and a
valid response
simultaneously in
response to the
same request.
"

Page 20

Revision 1.6

--- PAGE 29 ---

evertz

Application Note 65

Quartz Routing Switcher Remote Control Protocol


"Command
","Command Data
","Normal Response/s
","Note
"
"List Routes
",".L{level}{dest},-(cr)
",".A{level}{dest}, {srce}{level}{de


st}, {srce}
 level){dest , {srce (cr)
",
"Lock Destination
",".BL{dest (cr)
","No explicit response
",".BA response
conditional on
change being
made
"
"Unlock
Destination
",".BU{dest (cr)
","No explicit response
",".BA response
conditional on
change being
made
"
"Interrogate
Destination Lock
",".BI{dest)(cr)
",".BA{dest), {lock status)(cr)
",
"Fire Salvo
",".F{salvo}(cr)
",".A (cr)
",".U response/s
conditional on
route being made
"

☑

Note: Solicited and unsolicited destination route and lock update responses (.U and
.BA) will only be sent by the router if the control system has made changes.
All commands might return an error response if the syntax is incorrect.

Revision 1.6

Page 21

--- PAGE 30 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

This page left intentionally blank

Page 22

Revision 1.6

--- PAGE 31 ---

everlz

Application Note 65

Quartz Routing Switcher Remote Control Protocol

5.

CHANGES BY PANELS

If a panel changes an xpt, then the computer port outputs an update message as if the computer port
had changed the xpt.
If more than one level was used during the set xpt, then more messages will be
output.


"Panel
","Reply
"
"$L=1$, $D=2,$ $S=3$
",".UV002,003(cr)
"
"$L=1,2$ $D=20$, $S=12$
",".UV020,012(cr)
"
,".UA020,012(cr)
"
"$L=1,2,4~D=1$, $S=9$
",".UV001,009(cr)
"
,".UA001,009(cr)
"
,".UC001,009(cr)
"

Note that this message can be issued at any time, even just after the receipt of an external .SV
message.
Therefore the controlling computer software design must take this into account.
The update response created when a panel instigates a take will return all the levels in one message.
The use of multiple responses with single levels was superseded many years ago.

Revision 1.6

Page 23

--- PAGE 32 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

This page left intentionally blank

Page 24

Revision 1.6

--- PAGE 33 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

6.

EXAMPLES

Note that (cr) = ASCII carriage return (code OD hex).


"From Computer
","From Router
","Description
"
,"(cr).P(cr)
","power on or reset
"
".SV003,001 (cr)
",".UV003,001 (cr)
","set dest-3 to srce-1 on level-1
"
".SV3,1(cr)
",".UV003,001 (cr)
","set dest-3 to srce-1 on level-1
"
".SVAB02,001 (cr)
",".UVAB002,001 (cr)
","set dest-2 to srce-1 on levels-1,2,3
"
".SZ1,3(cr)
",".E(cr)
","level Z not allowed
"
".SB45,12
",".E(cr)
","dest to large
"
".IV1(cr)
",".AV001,001 (cr)
","get status of level-1 dest-1
"
".IA3(cr)
",".AA003,002(cr)
","get status of level-2, dest-3
"
".IVABC9(cr)
",".E(cr)
","only one level can be interrogated
"
".LV5,-(cr)
","AV005,002V006,009V007,010V008,


031V009,001V010,001V011,017V01
2,003(cr)
","get status of level-1 dest-5 to dest-12
"
".LB1,1(cr)
",".AB001,001B007,001B(cr)
","only dest-1 and dest-7 using srce-1
"
,".UV007,003(cr)
","system change by a control panel or


another computer interface
"
".#01 (cr)
",".A(cr)
","check to see if router connected
"
".?0C40(cr)
","A0C40,02,34,7F,2D,20,20,20,0D (cr)
","read of system setup data
"
".1328E,01,02,56,7F(cr)
","A328E,01,02,56,7F (cr)
","write of system setup data
"

Revision 1.6

Page 25

--- PAGE 34 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

This page left intentionally blank

Page 26

Revision 1.6

--- PAGE 35 ---

[Image 3]

evertz

Application Note 65

Quartz Routing Switcher Remote Control Protocol

7. TIMING (EMBEDDED CONTROL SYSTEMS)

With an embedded control system (V5 firmware) operating at 38400 baud the system can cope with an
overall crosspoint set rate of one change every 16ms when the serial link is directly into the master.
For
links into slave devices the timing and crosspoint set rate are dependent on the number of Q-Link
devices.
Vert
Sync

Line 6

RS-232
Data

Variable

Line 319

Line 6

0.5 to
15.5ms

Video switches

2.9ms (SV001,00x)
3.13ms (SVA001,00x) here (line 6)
at 34800 baud

The delay between the vertical sync and the RS232 message can be adjusted from 0.5ms to 12ms and
this has no effect on the video switch point.
Delays between 12ms and 18ms cause the video to start
switching in this frame OR the next frame some of the time.
A delay beyond 19ms causes the video
always to switch in the next frame.

Transmit times (calculated and tested):


"Baud Rate
","Serial Format
","1 byte time
","12 byte time
"
"38400
","1 start, 7 data, 1 stop
","0.234ms
","2.80ms
"
"38400
","1 start, 8 data, 1 stop
","0.260ms
","3.12ms
"
"9600
","1 start, 7 data, 1 stop
","0.938ms
","11.26ms
"
"9600
","1 start, 8 data, 1 stop
","1.041ms
","12.49ms
"

The current message structure packs the destination and source numbers out to 3 digits (001, 002).
The router will allow numbers without leading zeros (1,2).

Leading zeros can be removed which will vary the message length from 7 bytes (.SVA1,2cr) to 10 bytes
(.SVA32,32cr).
At 38400, 7 data this gives a message time of between 1.872ms and 2.340ms.
This
will also slightly reduce the routers internal processing time.

On an eight level system of $128\times128$, the maximum message length will be 18 bytes e.g.
SVABCDEFG123,123cr.

Revision 1.6

Page 27

--- PAGE 36 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

This page left intentionally blank

Page 28

Revision 1.6

--- PAGE 37 ---

[Image 4]

[Image 5]

evertz

8. OLDER SYSTEMS

Application Note 65

Quartz Routing Switcher Remote Control Protocol

Older products that do NOT use the FU-0003 processor must have a Computer Interface fitted before
the RS232/422 port can be used.
As this is an option, it must be ordered separately. RS232 or RS422
are link selectable on the Cl-0001.
There are two versions of this:


"PCB Number
","Link(s)
","RS-232
","RS-422
","Supplied
"
"PC106
","LK1-LK5
","Away from U3
","Toward U3
","Up to Dec 1996
"
"PC180
","SELECT
","232 position
","422 position
","Dec 1996 onwards
"

PC-106
LK1-5

PC-180
LK1

The older products operate at 9600, 8 data bits, no parity, and 1 stop bit.
Other baud rates and parity
options can be factory selected.

Revision 1.6

Page 29

--- PAGE 38 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

This page left intentionally blank

Page 30

Revision 1.6

--- PAGE 39 ---

everlz

9. APPENDIX A

Commands supported in different products:


,"Server
","higher


EQX


EQX


943


-


-
",,"FU
","higher


FC
","or


or


10
","or
"
"Commands


","

","build


or


SC1000


SC500E


10084.3




1.44


",,"higher


Xenon


or


5A22
","or


Xenon


b1211
","SC2000


et.11514.6


higher


higher


EQT


build


-


EMR


1.00
","SC2000


et.11686.1


higher


-


EQX
"





,"Magnum
","et.
",,,"0v5
","V4.0
","V4.10
"


".S
","Yes
","Yes


Yes
",,"Yes
","Yes
","Yes


Yes
","Yes
"
"M
","Yes
","$Yes^{6}$


Yes
",,"$Yes^{8}$
","Yes
","Yes


Yes
","Yes
"
"$\overline{BL^{3}}$
","Yes
","Yes


Yes
",,"No
","Yes
","Yes


Yes
","Yes
"
"$BU^{3}$
","Yes
","Yes


Yes
",,"Yes
","Yes
","Yes


Yes
","Yes
"
"$Bl^{4}$
","Yes
","Yes


Yes
",,"Yes
","Yes
","Yes


Yes
","Yes
"
".F
","Yes
","Yes


Yes
",,"Yes
","Yes
","Yes


Yes
","Yes
"
"1
","Yes
","Yes


Yes
",,"Yes
","Yes
","Yes


Yes
","Yes
"
"C
","No
","No


No
",,"No
","No
","No


No
","No
"
"$L^{1}$
","Yes
","Yes


No
",,"Yes
","No
","No


No
","No
"
".RD
","Yes
","Yes


Yes
",,"Yes
","Yes
","Yes


Yes
","Yes
"
"RS
","Yes
","Yes


Yes
",,"Yes
","Yes
","Yes


Yes
","Yes
"
".RL
","No
","Yes


No
",,"No
","Yes
","Yes


Yes
","Ye
Yes
"
".RE
","Yes
","Yes


No
",,"Yes
","Yes
","Yes


Yes
","Yes
"
"RT
","Yes
","Yes


No
",,"Yes
","Yes
","Yes


Yes
","Yes
"
".RM
","No
","Yes


No
",,"No
","Yes
","Yes


Yes
","Yes
"
"$\overline{WD}^{2}$
","No
","Yes


No
",,"Yes
","Yes
","Yes


Yes
","Yes
"
"$\overline{WS^{2}}$
","No
","Yes


No
",,"Yes
","Yes
","Yes


Yes
","Yes
"
".WL
","No
","Yes


No
",,"Yes
","Yes
","Yes


Yes
","Yes
"
"$NE^{2}$
","No
","Yes


No
",,"Yes
","Yes
","Yes


Yes
","Yes
"
"$\overline{WT^{2}}$
","No
","No


Yes
",,"Yes
","Yes
","Yes


Yes
","Yes
"
".WM
","No
","Yes


No
",,"No
","Yes
","Yes


Yes
","Yes
"
".?
","No
","No


No
",,"No
","No
","No


No
","No
"
" 
","No
","No


No
",,"No
","No
","No


No
","No
"
".QC
","No
","Yes


No
",,"No
","Yes
","Yes


Yes
","Yes
"
".QR
","No
","Yes


No
",,"No
","Yes
","Yes


Yes
","Yes
"
"QS
","No
","Yes


No
",,"No
","Yes
","Yes


Yes
","Yes
"
".QF
","No
","Yes


No
",,"No
","Yes
","Yes


N/R
","Yes
"
".QD
","No
","Yes


No
",,"No
","Yes
","Yes


Yes
","Yes
"
"QL
","No
","Yes


No
",,"No
","Yes
","Yes


Yes
","Yes
"
".#00
","No
","Yes


No
",,"Yes
","No
","No


No
","No
"
"#01
","No
","Yes


Yes
",,"Yes
","Yes
","Yes


Yes
","Yes
"
".#12
","No
","Yes


No
",,"Yes
","Yes
","Yes


Yes
","Yes
"
".#23
","No
","No


No
",,"Yes
","No
","No


No
","No
"
".#34
","No
","No


No
",,"Yes
","No
","No


No
","No
"
".#40
","No
","No


No
",,"Yes
","No
","No


No
","No
"
".#41
","No
","No


No
",,"Yes
","No
","No


No
","No
"
"#42
","No
","No


No
",,"Yes
","No
","No


No
","No
"
".#43
","No
","No


No
",,"Yes
","No
","No


No
","No
"

Application Note 65

Quartz Routing Switcher Remote Control Protocol

Revision 1.6

Page 31

--- PAGE 40 ---


,,"higher


EQX
","EQX


943


FU
","higher


FC
",,,
"Commands
","Magnum


Server
","or


-
","higher


build


-


Xenon


or
","or


Xenon


b1211
","





","





","





"





,,"SC500E


et.10084.3
","SC1000


5A22


1.44
","0v5
",,,
"#.44
","No
","No
","Yes


No
","No
","No
","No
","No
"
"#.45
","No
","No
","Yes


No
","No
","No
","No
","No
"
"#.46
","No
","No
","No


Yes
","No
","No
","No
","No
"
".#47
","No
","No
","Yes


No
","No
","No
","No
","No
"
".#48
","No
","No
","Yes


No
","No
","No
","No
","No
"
"#.49
","No
","No
","Yes


No
","No
","No
","No
","No
"
".#50
","No
","No
","Yes


No
","No
","No
","No
","No
"
"#.51
","No
","No
","Yes


No
","No
","No
","No
","No
"
"#.56
","No
","No
","Yes


No
","No
","No
","No
","No
"
".#67
","No
","No
","Yes


No
","No
","No
","No
","No
"
".#78
","No
","No
","Yes


No
","No
","No
","No
","No
"
".#80,75
","No
","No
","No


Yes
","No
","No
","No
","No
"
".#81
","No
","No
","Yes


No
","No
","No
","No
","No
"
"VP-
","No
","No
","No


No
","No
","No
","No
","No
"
".VP+
","No
","No
","No


No
","No
","No
","No
","No
"
".VP
","No
","No
","No


No
","No
","No
","No
","No
"
".&LOCALTCPIP
","No
","No
","Yes


Yes
","No
","No
","No
","No
"
".&REMOTETCPIP
","No
","No
","Yes


No
","No
","No
","No
","No
"
".&TCPGATE
","No
","No
","Yes


Yes
","No
","No
","No
","No
"
".&TCPMASK
","No
","No
","No


No
","No
","No
","No
","No
"
".&TC
.&TAKECONTROL


.&IC
","N/E


N/E


A/T
","$Yes^{9}$


Yes


Yes 10
","Yes


No


No


Yes


Yes


No
","Yes


Yes


Yes
","No


No


No
","N/E
Yes 11


Yes
","N/E
Yes 12


Yes
"
".&INCONTROL
Responses
","A/T
","Yes 10
","Yes


No
","Yes
","No
","Yes
","Yes
"
"A
","Yes
","Yes
","Yes


Yes
","Yes
","Yes
","Yes
","Yes
"
".E


.U
","Yes


Yes
","Yes


Yes
","Yes


Yes


Yes


Yes
","Yes


Yes
","Yes


Yes
","Yes
Yes
","Yes
Yes
"
".R
","Yes
","Yes
","Yes


Yes
","Yes
","Yes
","Yes
","Yes
"
"B


.P
","Yes


Yes
","Yes


No
","Yes


Yes


Yes


No
","Yes


Yes
","Yes
No
","Yes
No
","Yes
No
"
".&
","Yes
","No
","Yes


Yes
","No
","No
","No
","No
"
".\$
","Yes
","No
","No


No
","No
","No
","Yes
","Yes
"

Application Note 65

Quartz Routing Switcher Remote Control Protocol

Page 32

Revision 1.6

evertz

--- PAGE 41 ---

evertz

Notes:

Application Note 65

Quartz Routing Switcher Remote Control Protocol

1. X-Link cards, 7700-R16X16 cards, XRF-1 routers, and XRF-6 routers do not support this
command.
2. Some devices are not supporting all write commands.

3. Some routers (in particular, old EQX FCs) do not respond to BL and .BU commands.
When
performing these operations an additional.BI command is sent to compensate.
4. Topaz routers do not respond to this message, so it assumes that everything is unlocked on
start up.
5. The fire salvo command is only available over Quartz Protocol (not the standard Quartz
interface).
The numerical mapping of salvos is determined by the web configuration tool, and
stored in the configuration file.
6. There is no A in return in the codes prior to August 2011

7. Time stamped for ZDF, SC code only.
8. Only 16 routes at a time.

9. Not supported in multi-router systems.
10. In multi-router systems, it always responds "In control".

11. Will put both SC2000 in dual-hot mode ".&RELEASECONTROL" command is required to

release on SC2000

12. Redundant SC-2000 is not supported in this build.
EQX-SC2000 will always be in control

Legends:


"$N/E=$
","No effect
"
"$A\sqrt{T}=$
","Always True
"

Revision 1.6

Page 33

--- PAGE 42 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

evertz

This page left intentionally blank

Page 34

Revision 1.6

--- PAGE 43 ---

evertz

Application Note 65

Quartz Routing Switcher Remote Control Protocol

10. ADDENDUM: MAGNUM COMMAND EXTENTIONS

All extensions will have the following syntax and only exist when connecting to a MAGNUM system:

.X,CMD[, ARGS]

Extension responses will be one the following:

.XU,CMD, ARGS]

.XA,CMD[,ARGS]

.XE, CMD[, ARGS), MESSAGE

.XU and .XA both represent a successful completion of the command.
.XA responses are only sent to
the requesting client. .XU responses are notifications that are sent to all connected clients.
This means
clients must be able to handle unsoliticted.XU messages.

10.1.
EXTENSION SUPPORT QUERY

Query if an extended command is supported (and enable it if so):

.X,QCX,CMD1,CMD2

.XA,QCX, CMD1,OK, CMD2, NO

In the above case CMD1 is supported but CMD2 is not.
In general, clients cannot use
extended commands that were not checked via this command.

10.2.
LOCKS AND PROTECTS

Output locks and protects with extended information for tracking who holds the lock/protect.
As the
base protocol supports locks and protects servers will only enable the extension if the client queries
for the presence of the extended commands, i.e. .X,QCX, ILK, ELK, DLK, EPT, DPT, in which case the
server will not send.BU updates.
ENABLE LOCK:

.X,ELK, LEVELS, DESTNUM, IDENT, NAME
.XU, ELK, LEVELS, DESTNUM, IDENT, NAME

ENABLE PROTECT:

.X,EPT, LEVELS, DESTNUM, IDENT, 1 NAME
.XU,EPT, LEVELS, DESTNUM, IDENT, NAME

DISABLE LOCK:

.X,DLK, LEVELS, DESTNUM, IDENT, NAME
.XU, DLK, LEVELS, DESTNUM, IDENT, NAME

DISABLE PROTECT:

.X,DPT, LEVELS, DESTNUM, IDENT, NAME
.XU, DPT, LEVELS, DESTNUM, IDENT, NAME

INTERROGATE LOCK/PROTECT STATUS:

.X, ILK, LEVELS, DESTNUM

.XA, ELK, LEVELS, DESTNUM, IDENT, NAME
.XA, DLK, OTHER_LEVELS, DESTNUM, IDENT, NAME

(etc.)

Revision 1.6

Page 35

--- PAGE 44 ---

Application Note 65

Quartz Routing Switcher Remote Control Protocol

everlz

If the interrogate command requests multiple levels, there may be multiple reponses (at least one
per different status that exists on the port specified by DESTNUM).
All errors follow the standard format:

Quartz Remote Control Protocol Version 1 + Extensions Revision 2

Page 36

Revision 1.6