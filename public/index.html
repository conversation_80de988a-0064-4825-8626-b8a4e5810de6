<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Router Control</title>
    <style>
        :root {
            --monitor-button-size: 100px;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #a0a0a0; /* Darker grey */
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            background-color: #a0a0a0; /* Darker grey */
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); /* Subtle shadow for depth */
            width: 100%;
            max-width: none; /* Remove any max-width constraint */
        }

        /* Tab Navigation: 3 larger square buttons, centered at top middle */
        .tab-navigation {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            gap: 16px;
            width: 100%;
            position: sticky;
            top: 0;
            background-color: #a0a0a0;
            padding: 10px 0;
            z-index: 100;
        }
        .tab-button::before {
            display: none;
        }
        .tab-button::after {
            display: none !important;
        }
        .tab-button {
            width: 128px;
            height: 128px;
            min-width: 128px;
            min-height: 128px;
            max-width: 128px;
            max-height: 128px;
            font-size: 5px;
            border-radius: 16px;
            letter-spacing: 0.5px;
            white-space: normal;
            line-height: 1.1;
            padding: 6px 8px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .tab-button span {
            font-size: 5px;
            font-weight: bold;
            line-height: 1.1;
            word-break: break-word;
            width: 100%;
        }
        .tab-button.active {
            background: #ffc107 !important;
            color: #222 !important;
            border: 2.5px solid #a0a0a0 !important;
            box-shadow: none !important;
        }
        .tab-button.active span {
            color: #222 !important;
        }
        .tab-button:not(.active) {
            background: #e0e0e0;
            color: #c0c0c0;
            border: 2.5px solid #ffc107;
        }
        .tab-button:not(.active) span {
            color: #c0c0c0;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            background-color: #a0a0a0; /* Darker grey to match the rest of the UI */
            padding: 10px;
            border-radius: 5px;
            border: 2px solid #a0a0a0; /* Invisible outline */
        }
        .status-indicator {
            display: flex;
            align-items: center;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background-color: #5cb85c; }
        .status-disconnected { background-color: #d9534f; }
        .connect-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .connect-button {
            padding: 8px 15px;
            font-weight: bold;
        }
        .connect {
            background-color: #5cb85c;
            color: white;
        }
        .disconnect {
            background-color: #d9534f;
            color: white;
        }
        .control-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .panel-section {
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            padding: 15px;
            background-color: #a0a0a0; /* Darker grey for all sections */
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .router-section-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .router-section-container {
            border: 2px solid #ffc107;
            border-radius: 8px;
            background: #a0a0a0;
            padding: 15px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .routing-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .routing-column {
            flex: 1;
        }
        h2 {
            margin-top: 0;
        }
        .button-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }
        button {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 5px;
            border: 1px solid #ccc;
            background-color: #f8f8f8;
            font-size: 14px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: 0.3s;
        }

        button:hover {
            background-color: #e8e8e8;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        button:hover::after {
            left: 100%;
        }

        button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        button.input {
            background-color: #d4edda;
        }
        button.input.active {
            background-color: #5cb85c;
            color: white;
        }
        button.output {
            background-color: #d1ecf1;
        }
        button.output.active {
            background-color: #17a2b8;
            color: white;
        }
        button.take {
            background-color: #28a745; /* Changed from red to green */
            color: white;
            font-weight: bold;
        }
        button.take:hover {
            background-color: #218838; /* Darker green on hover */
        }
        button.take:disabled {
            background-color: #d4edda;
            cursor: not-allowed;
        }
        button.lock {
            background-color: #dc3545; /* Changed from yellow to red */
            color: white;
        }
        button.lock:hover {
            background-color: #c82333; /* Darker red on hover */
        }
        button.lock.active {
            background-color: #bd2130; /* Even darker red when active */
            color: white;
        }
        button.refresh, button.lock, button.take {
            height: 48px;
            min-width: 90px;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .log-panel {
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            padding: 10px;
            height: 150px;
            overflow-y: auto;
            background-color: #a0a0a0; /* Darker grey */
            margin-top: 20px;
        }
        .log-entry {
            margin-bottom: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry.command { color: #007bff; }
        .log-entry.response { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        select {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ccc;
            font-size: 14px;
            width: 100%;
            max-width: 300px;
            margin-bottom: 15px;
        }
        select#destination-select,
        select#source-select,
        #wall-source-select {
            display: none !important;
        }
        .status-panel {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .lock-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #dc3545;
            margin-left: 10px;
        }
        .lock-indicator.hidden {
            display: none;
        }
        .flex-row {
            display: flex;
            align-items: center;
        }
        .category-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .category-button {
            padding: 8px 12px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 5px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .category-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: 0.3s;
        }

        .category-button:hover {
            background-color: #e0e0e0;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .category-button:hover::after {
            left: 100%;
        }

        .category-button.active {
            background-color: #ffc107;
            color: #333;
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
        }

        .status-window {
            display: flex;
            justify-content: space-between;
            background-color: #000000;
            color: #ffc107;
            padding: 12px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-family: monospace;
            font-size: 14px;
            border: 2px solid #333;
        }
        .status-window.locked {
            border: 2px solid #FF0000;
        }
        .status-label {
            font-weight: bold;
        }
        .square-button-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 12px;
            min-height: 210px; /* Height to accommodate 3 rows of buttons (60px + 8px gap) * 3 */
            margin-bottom: 15px;
            overflow-y: auto;
        }
        .square-button {
            width: 70px; /* Increased from 60px to accommodate 8 characters */
            height: 70px; /* Increased height to maintain square proportion */
            margin: 3px;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            text-align: center;
            cursor: pointer;
            border-radius: 5px;
            padding: 4px;
            word-break: break-word;
            background-color: #c0c0c0; /* Silver background */
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .square-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: 0.4s;
        }

        .square-button:hover {
            background-color: #b0b0b0;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .square-button:hover::after {
            left: 100%;
        }

        .square-button.selected {
            border: 2px solid #007bff;
            background-color: #e7f1ff;
        }
        .square-button.output {
            background-color: #d1ecf1;
        }
        .square-button.output.selected {
            background-color: #ffc107;
            color: #333;
        }
        .square-button.input {
            background-color: #d4edda;
        }
        .square-button.input.selected {
            background-color: #ffc107;
            color: #333;
        }
        .locked-text {
            color: #dc3545; /* Red text */
            font-weight: bold;
            margin-left: 5px;
            display: none; /* Hide by default */
        }

        /* Category Management Styles - Updated for side by side layout */
        .category-management {
            margin-top: 20px;
        }

        .category-form {
            background-color: #a0a0a0;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 2px solid #ffc107; /* Yellow outline */
        }

        .category-side-by-side {
            display: flex;
            gap: 20px;
            justify-content: space-between;
        }

        .category-column {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            flex: 1;
        }

        .category-column > div:first-child {
            width: 100%;
        }

        .category-list {
            /* max-height: 400px; */
            /* overflow-y: auto; */
        }

        .category-item {
            background-color: #a0a0a0;
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            padding: 12px;
            margin-bottom: 10px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .category-item .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .category-item .category-name {
            font-weight: bold;
            font-size: 16px;
        }

        .category-item .category-controls {
            display: flex;
            gap: 5px;
        }

        .category-item .edit-btn,
        .category-item .delete-btn {
            padding: 4px 8px;
            font-size: 12px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }

        .category-item .edit-btn:hover {
            background-color: #007bff;
            color: white;
        }

        .category-item .delete-btn:hover {
            background-color: #dc3545;
            color: white;
        }

        .category-item .io-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .category-item .io-tag {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 3px 6px;
            font-size: 12px;
        }

        .ios-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
            padding: 10px;
            background-color: #a0a0a0;
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .ios-selector .io-item.selected {
            background-color: #28a745 !important;
            color: #fff !important;
            border: 2px solid #218838 !important;
        }

        .section-header.category-title {
            color: #c0c0c0 !important;
            background: transparent !important;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            letter-spacing: 2px;
            text-transform: uppercase;
            justify-content: center;
            border: none !important;
            box-shadow: none;
            width: 100%;
            text-shadow: 0 0 2px #000, 0 0 1px #000, 1px 1px 2px #000, -1px -1px 2px #000;
        }

        /* Monitors Tab Styles */
        .monitors-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .monitors-toolbar {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background-color: #a0a0a0; /* Darker grey to match the rest of the UI */
            border-radius: 5px;
            margin-bottom: 10px;
            border: 2px solid #a0a0a0 !important; /* Grey outline */
        }

        .size-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
            flex-grow: 1;
        }

        .size-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .size-control span {
            min-width: 50px;
            text-align: center;
        }

        .size-control input[type="range"] {
            width: 150px;
        }

        .monitor-board {
            min-height: 800px;
            width: 100%;
            background-color: #a0a0a0;
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            padding: 20px;
            position: relative;
        }

        .monitor-button {
            position: absolute;
            /* Remove width/height from CSS var since each button will have individual size */
            background-color: #c0c0c0; /* Silver background */
            border: 2px solid #ffc107;
            border-radius: 5px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: move;
            user-select: none;
            padding: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: width 0.3s, height 0.3s;
        }

        /* Monitor button controls */
        .monitor-button-controls {
            position: absolute;
            top: 5px;
            right: 5px;
            display: none;
            gap: 5px;
        }
        
        .monitor-button:hover .monitor-button-controls,
        .monitor-button.selected .monitor-button-controls {
            display: flex;
        }
        
        .monitor-button-rename,
        .monitor-button-remove,
        .monitor-button-copy,
        .monitor-button-paste {
            width: 20px;
            height: 20px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        
        .monitor-button-rename:hover {
            background-color: #f8f8f8;
        }
        
        .monitor-button-remove:hover {
            background-color: #ff6b6b;
            color: white;
        }

        .monitor-button-copy:hover {
            background-color: #4caf50;
            color: white;
        }

        .monitor-button-paste:hover {
            background-color: #2196f3;
            color: white;
        }

        .monitor-button-label {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }

        .monitor-button-assignment {
            font-size: 12px;
            text-align: center;
            color: #333;
            background-color: #f0f0f0;
            padding: 2px 5px;
            border-radius: 3px;
            width: 90%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .monitor-button.destination-assigned {
            background-color: #d1ecf1;
            border-color: #17a2b8;
        }

        .monitor-button.source-assigned {
            background-color: #d4edda;
            border-color: #28a745;
        }

        .monitor-button.selected {
            border-color: #ffc107 !important;
            box-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
        }

        /* Selected monitor button styling */
        .monitor-button.selected {
            border: 2px solid #28a745;
            box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
        }
        
        /* Assignment mode styling */
        .monitor-board.assignment-mode {
            background-color: rgba(0, 123, 255, 0.05);
        }
        
        /* Toast notification styles */
        .toast-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .toast {
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            color: white;
            opacity: 0;
            transition: opacity 0.3s;
            max-width: 300px;
        }
        
        .toast.show {
            opacity: 1;
        }
        
        .toast.success {
            background-color: #28a745;
        }
        
        .toast.error {
            background-color: #dc3545;
        }
        
        .toast.info {
            background-color: #17a2b8;
        }

        .form-row {
            margin-bottom: 15px;
        }

        .action-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .action-button:hover {
            background-color: #0056b3;
        }

        #copy-button-btn, #paste-button-btn {
            background-color: #6c757d;
        }

        #copy-button-btn:hover, #paste-button-btn:hover {
            background-color: #545b62;
        }

        .section-header {
            color: #c0c0c0 !important;
            background: transparent !important;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            letter-spacing: 2px;
            text-transform: uppercase;
            justify-content: center;
            border: none !important;
            box-shadow: none;
            width: 100%;
            text-shadow: 0 0 2px #000, 0 0 1px #000, 1px 1px 2px #000, -1px -1px 2px #000;
        }

        /* Monitor Wall Sources Title Customization */
        #monitor-wall .routing-column .section-header {
            color: #c0c0c0 !important;
            background: transparent !important;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            letter-spacing: 2px;
            text-transform: uppercase;
            justify-content: center;
            border: none !important;
            box-shadow: none;
            width: 100%;
            text-shadow: 0 0 2px #000, 0 0 1px #000, 1px 1px 2px #000;
        }
        #monitor-wall .routing-column .section-header .wall-take-btn {
            background-color: #28a745;
            color: white;
            font-weight: bold;
            padding: 10px 18px;
            border-radius: 5px;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
            transition: background 0.2s;
            font-size: 16px;
            left: 10px;
            position: absolute;
        }
        #monitor-wall .routing-column .section-header .wall-take-btn:disabled {
            background-color: #d4edda;
            color: #888;
        }
        #monitor-wall .routing-column .section-header .wall-take-btn:hover:not(:disabled) {
            background-color: #218838;
        }

        /* Wall board resize handle */
        #wall-board-resize-handle {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 24px;
            height: 24px;
            background: rgba(0,0,0,0.15);
            border-radius: 0 0 5px 0;
            cursor: nwse-resize;
            z-index: 1000;
            display: block;
        }
        #wall-board-resize-handle:after {
            content: '';
            display: block;
            width: 16px;
            height: 16px;
            border-right: 3px solid #007bff;
            border-bottom: 3px solid #007bff;
            position: absolute;
            right: 4px;
            bottom: 4px;
            border-radius: 0 0 4px 0;
        }

        .side-by-side-panel {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .current-state-panel {
            flex: 1 1 0;
            max-width: 50%;
            min-width: 0;
        }
        .log-panel-section {
            flex: 1 1 0;
            max-width: 50%;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }
        .log-panel-section .log-panel {
            flex: 1 1 0;
            min-height: 150px;
            height: 100%;
        }
        @media (max-width: 900px) {
            .side-by-side-panel {
                flex-direction: column;
            }
            .current-state-panel, .log-panel-section {
                max-width: 100%;
            }
        }

        /* Monitor Wall Tab: Make all outlines (borders) except the layout canvas match the background grey */
        #monitor-wall .panel-section,
        #monitor-wall .section-header,
        #monitor-wall .category-buttons,
        #monitor-wall .routing-column,
        #monitor-wall .square-button-container,
        #monitor-wall .category-list,
        #monitor-wall .category-column,
        #monitor-wall .monitors-toolbar {
            border-color: #a0a0a0 !important;
        }
        #monitor-wall .monitor-board {
            border-color: #ffc107 !important; /* Keep yellow outline for layout canvas */
        }

        #monitor-wall .category-buttons#wall-source-categories {
            margin-top: 10px;
        }

        #monitor-wall .category-button.active {
            background-color: #ffc107;
            color: #333;
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
        }

        #wall-source-buttons .square-button.selected {
            background-color: #ffc107;
            color: #333;
        }

        .main-title, .router-section-title, .category-main-title, .category-form-title, .monitor-wall-title, .category-title-center, .log-title, .current-state-title, .tab-button, .section-header.category-title, .category-title-center, #monitor-wall .routing-column .section-header, .tab-navigation .tab-button {
            color: #c0c0c0;
            background: transparent;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            letter-spacing: 2px;
            margin-bottom: 20px;
            text-transform: uppercase;
            width: 100%;
            text-shadow: 0 0 2px #000, 0 0 1px #000, 1px 1px 2px #000, -1px -1px 2px #000;
        }

        /* Monitor Wall: created window, copy/paste, lock, etc. */
        #monitor-wall .action-button,
        #monitor-wall .action-button:active,
        #monitor-wall .action-button:focus {
            background-color: #ffc107 !important;
            color: #333 !important;
            border: 1px solid #ffc107 !important;
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.18);
        }
        #monitor-wall .action-button:hover {
            background-color: #ffdb5b !important;
            color: #222 !important;
        }
        /* Category: add category buttons yellow */
        #add-destination-category-btn,
        #add-source-category-btn {
            background-color: #ffc107 !important;
            color: #333 !important;
            border: 1px solid #ffc107 !important;
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.18);
        }
        #add-destination-category-btn:hover,
        #add-source-category-btn:hover {
            background-color: #ffdb5b !important;
            color: #222 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Tab Navigation - Moved to top and made square -->
        <div class="tab-navigation">
            <button class="tab-button active" data-tab="router-control">Router Control</button>
            <button class="tab-button" data-tab="category-management">Category</button>
            <button class="tab-button" data-tab="monitor-wall">Monitor Wall</button>
        </div>
        
        <!-- Router Control Tab -->
        <div id="router-control" class="tab-content active">
            <div class="status-bar">
                <div class="status-indicator">
                    <div id="status-dot" class="status-dot status-disconnected"></div>
                    <div id="status-text">Disconnected</div>
                </div>
                <div>
                    <span>Router: 192.168.0.180:4000</span>
                    <div class="connect-buttons">
                        <button id="connect-btn" class="connect-button connect">Connect</button>
                        <button id="disconnect-btn" class="connect-button disconnect">Disconnect</button>
                    </div>
                </div>
            </div>
            
            <div class="control-panel">
                <div class="side-by-side-panel">
                    <div class="panel-section current-state-panel">
                        <h2 class="current-state-title">Current State</h2>
                        <div class="status-window">
                            <div class="status-label">Current Output:</div>
                            <div id="selected-output-name">Output 1</div>
                        </div>
                        <div id="input-status-window" class="status-window">
                            <div class="status-label">Routed Input:</div>
                            <div id="output-source">Unknown</div>
                            <div id="lock-text" class="locked-text">LOCKED</div>
                        </div>
                        <div class="button-row" style="align-items: flex-start;">
                            <button id="refresh-btn" class="refresh">Refresh Status</button>
                            <button id="lock-btn" class="lock">LOCK</button>
                            <button id="take-btn" class="take" disabled>TAKE</button>
                        </div>
                    </div>
                    <div class="panel-section log-panel-section">
                        <h2 class="log-title">Log</h2>
                        <div id="log-panel" class="log-panel"></div>
                    </div>
                </div>
                
                <div class="router-section-row">
                    <div class="router-section-container">
                        <div class="router-section-title">Sources</div>
                        <div class="category-buttons" id="source-categories">
                            <button class="category-button" data-category="ALL">ALL</button>
                            <button class="category-button" data-category="CCU">CCU</button>
                            <button class="category-button" data-category="CCU_CHAR">CCU CHAR</button>
                            <button class="category-button" data-category="CCU_SSL">CCU SSL</button>
                            <button class="category-button" data-category="SWITCHER">SWITCHER</button>
                            <button class="category-button" data-category="MUX">MUX</button>
                            <button class="category-button" data-category="EXT">EXT</button>
                            <button class="category-button" data-category="EVS_1">EVS 1</button>
                            <button class="category-button" data-category="EVS_2">EVS 2</button>
                            <button class="category-button" data-category="EVS_3">EVS 3</button>
                            <button class="category-button" data-category="VTR">VTR</button>
                            <button class="category-button" data-category="WF">WF</button>
                            <button class="category-button" data-category="PC_OUT">PC OUT</button>
                        </div>
                        <div id="source-buttons" class="square-button-container">
                            <!-- Source buttons will be generated here -->
                        </div>
                        <select id="source-select">
                            <option value="">-- Select Source --</option>
                            <!-- Will be populated from JavaScript -->
                        </select>
                    </div>
                    <div class="router-section-container">
                        <div class="router-section-title">Destinations</div>
                        <div class="category-buttons" id="destination-categories">
                            <button class="category-button" data-category="ALL">ALL</button>
                            <button class="category-button" data-category="SWITCHER">SWITCHER</button>
                            <button class="category-button" data-category="MUX">MUX</button>
                            <button class="category-button" data-category="LINES">LINES</button>
                            <button class="category-button" data-category="EVS1">EVS1</button>
                            <button class="category-button" data-category="EVS2">EVS2</button>
                            <button class="category-button" data-category="CAR_DDA">CAR DDA</button>
                            <button class="category-button" data-category="WF">WF</button>
                            <button class="category-button" data-category="SLM">SLM</button>
                            <button class="category-button" data-category="RVM">RVM</button>
                            <button class="category-button" data-category="VTR">VTR</button>
                            <button class="category-button" data-category="CCU_RET">CCU RET</button>
                            <button class="category-button" data-category="PC_IN">PC IN</button>
                        </div>
                        <div id="destination-buttons" class="square-button-container">
                            <!-- Destination buttons will be generated here -->
                        </div>
                        <select id="destination-select">
                            <option value="">-- Select Destination --</option>
                            <!-- Will be populated from JavaScript -->
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Category Management Tab -->
        <div id="category-management" class="tab-content">
            <div class="panel-section" style="border:none;">
                <div class="category-form">
                    <h3 class="category-form-title" style="text-align:center; color:#c0c0c0; text-transform:uppercase; font-size:22px; font-weight:bold; letter-spacing:2px; background:transparent; margin-bottom:18px;">CREATE CATEGORY</h3>
                    
                    <div class="form-row">
                        <label for="category-type">Category Type:</label>
                        <select id="category-type">
                            <option value="destination">Destination (Output)</option>
                            <option value="source">Source (Input)</option>
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <label for="category-name">Category Name:</label>
                        <input type="text" id="category-name" placeholder="Enter category name">
                    </div>
                    
                    <div class="form-row">
                        <label id="ios-selector-label">Select I/Os for this category:</label>
                        <div class="ios-selector" id="ios-selector">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button id="save-category-btn" class="save">Save Category</button>
                        <button id="cancel-category-btn" class="cancel">Cancel</button>
                    </div>
                </div>
                
                <!-- Side by side category management -->
                <div class="category-side-by-side">
                    <div class="category-column">
                        <div style="display: flex; align-items: center; margin-bottom: 10px;">
                            <button id="add-destination-category-btn" class="action-button">Add Destination Category</button>
                            <div class="section-header category-title">Destination Categories</div>
                        </div>
                        <div id="destination-category-list" class="category-list">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                    <div class="category-column">
                        <div style="display: flex; align-items: center; margin-bottom: 10px;">
                            <button id="add-source-category-btn" class="action-button">Add Source Category</button>
                            <div class="section-header category-title">Source Categories</div>
                        </div>
                        <div id="source-category-list" class="category-list">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Monitors Tab -->
        <div id="monitors" class="tab-content">
            <div class="panel-section">
                <h2>Monitors Control Panel</h2>
                <div class="monitors-container">
                    <div class="monitors-toolbar">
                        <button id="create-monitor-btn" class="action-button">Create Monitor</button>
                        <button id="copy-button-btn" class="action-button">Copy</button>
                        <button id="paste-button-btn" class="action-button">Paste</button>
                        <button id="save-ui-btn" class="action-button">Save UI</button>
                        <button id="load-ui-btn" class="action-button">Load UI</button>
                        <div class="size-controls">
                            <div class="size-control">
                                <span>Width:</span>
                                <input type="range" id="button-width-slider" min="50" max="200" value="100">
                                <span id="button-width-value">100px</span>
                            </div>
                            <div class="size-control">
                                <span>Height:</span>
                                <input type="range" id="button-height-slider" min="50" max="200" value="100">
                                <span id="button-height-value">100px</span>
                            </div>
                        </div>
                    </div>
                    <div id="monitor-board" class="monitor-board"></div>
                    <div id="assignment-controls" style="display: none;">
                        <select id="assignment-type">
                            <option value="destination">Destination</option>
                            <option value="source">Source</option>
                        </select>
                        <select id="assignment-options"></select>
                        <button id="start-assignment-btn" class="action-button">Assign</button>
                        <button id="cancel-assignment-btn" class="action-button">Cancel</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monitor Wall Tab -->
        <div id="monitor-wall" class="tab-content">
            <div class="panel-section">
                <h2 class="monitor-wall-title">Monitor Wall</h2>
                <div class="monitors-container">
                    <div class="monitors-toolbar">
                        <button id="create-wall-window-btn" class="action-button">Create Window</button>
                        <button id="lock-wall-btn" class="action-button">Lock Windows</button>
                        <button id="copy-wall-size-btn" class="action-button">Copy</button>
                        <button id="paste-wall-size-btn" class="action-button">Paste</button>
                        <button id="save-all-btn" class="action-button">Save All</button>
                        <button id="load-all-btn" class="action-button">Load All</button>
                        <button id="export-all-btn" class="action-button">Export All</button>
                        <button id="import-all-btn" class="action-button">Import All</button>
                        <input type="file" id="import-all-file" style="display:none" accept="application/json">
                        <label style="margin-left:16px;">Columns: <input type="number" id="wall-cols-input" min="1" max="20" value="6" style="width:50px;"></label>
                        <label style="margin-left:8px;">Rows: <input type="number" id="wall-rows-input" min="1" max="20" value="4" style="width:50px;"></label>
                        <div class="size-controls">
                            <div class="size-control">
                                <span>Width:</span>
                                <input type="range" id="wall-window-width-slider" min="50" max="400" value="200">
                                <span id="wall-window-width-value">200px</span>
                            </div>
                            <div class="size-control">
                                <span>Height:</span>
                                <input type="range" id="wall-window-height-slider" min="50" max="400" value="150">
                                <span id="wall-window-height-value">150px</span>
                            </div>
                        </div>
                    </div>
                    <div id="monitor-wall-board" class="monitor-board" style="position: relative;">
                        <div id="wall-board-resize-handle"></div>
                    </div>
                    <div style="margin-top: 0;">
                        <div class="panel-section" style="margin-top: 0;">
                            <div class="routing-container">
                                <div class="routing-column">
                                    <div class="section-header" style="position: relative; display: flex; align-items: center; justify-content: center; gap: 16px;">
                                        <span class="sources-title">Sources</span>
                                        <button id="wall-take-btn" class="take" disabled style="margin-left: 16px; position: static;">TAKE</button>
                                        <label style="margin-left:10px; display:flex; align-items:center; font-size:14px;">
                                            <input type="checkbox" id="wall-take-confirm-checkbox" checked style="margin-right:4px;">
                                            Active Take
                                        </label>
                                    </div>
                                    <div class="category-buttons" id="wall-source-categories"></div>
                                    <div id="wall-source-buttons" class="square-button-container"></div>
                                    <select id="wall-source-select">
                                        <option value="">-- Select Source --</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>